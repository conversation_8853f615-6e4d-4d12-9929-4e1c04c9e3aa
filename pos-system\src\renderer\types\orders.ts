export type OrderStatus = 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
export type OrderType = 'dine-in' | 'takeaway' | 'delivery';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
export type PaymentMethod = 'cash' | 'card' | 'digital';

// Order item options interface
interface OrderItemOption {
  id: string;
  name: string;
  value: string | number | boolean;
  price?: number;
  category?: string;
}

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  options?: OrderItemOption[];
}

export interface OrderPricing {
  subtotal: number;
  deliveryFee: number;
  pickupDiscount: number;
  serviceFee: number;
  taxAmount: number;
  totalAmount: number;
  deliveryZoneId?: string | null;
  deliveryZoneName?: string | null;
  pricingCalculatedAt?: string | null;
  pricingVersion?: string | null;
  estimatedTime?: {
    min: number;
    max: number;
    message: string;
  } | null;
}

export interface Order {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  items: OrderItem[];
  totalAmount: number;
  customerName?: string;
  customerPhone?: string;
  orderType: OrderType;
  tableNumber?: string;
  address?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  estimatedTime?: number; // in minutes (for backward compatibility)
  paymentStatus?: PaymentStatus;
  paymentMethod?: PaymentMethod;
  paymentTransactionId?: string;
  
  // Enhanced pricing breakdown
  pricing?: OrderPricing;
  subtotal?: number;
  deliveryFee?: number;
  pickupDiscount?: number;
  serviceFee?: number;
  taxAmount?: number;
  deliveryZoneId?: string | null;
  pricingCalculatedAt?: string | null;
  pricingVersion?: string | null;
}