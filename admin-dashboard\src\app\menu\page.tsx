'use client'

import React, { useState, useEffect } from 'react'
import { supabase, createServerSupabaseClient } from '@/lib/supabase'
import { toast } from 'react-hot-toast'
import { useTheme } from '@/contexts/theme-context'
import CategoryModal from '@/components/menu/CategoryModal'
import IngredientModal from '@/components/menu/IngredientModal'
import SubcategoryModal from '@/components/menu/SubcategoryModal'
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Eye, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Package, 
  Tag, 
  ChefHat, 
  DollarSign,
  Clock,
  Star,
  AlertTriangle,
  RefreshCw,
  Save,
  X,
  Coffee,
  Calendar
} from 'lucide-react'

// Enhanced interfaces matching our database schema
interface MenuCategory {
  id: string
  name: string
  description?: string
  parent_id?: string
  category_type: 'standard' | 'customizable'
  display_order: number
  image_url?: string
  is_active: boolean
  is_featured: boolean
  created_at: string
  updated_at: string
}

interface IngredientCategory {
  id: string
  name: string
  description?: string
  color_code: string
  display_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface Ingredient {
  id: string
  category_id: string
  name: string
  description?: string
  price: number
  cost: number
  image_url?: string
  stock_quantity: number
  min_stock_level: number
  is_available: boolean
  allergens?: string[]
  nutritional_info?: any
  display_order: number
  created_at: string
  updated_at: string
}

interface Subcategory {
  id: string
  category_id: string
  name: string
  description?: string
  base_price: number
  cost: number
  image_url?: string
  preparation_time: number
  calories: number
  allergens?: string[]
  nutritional_info?: any
  is_available: boolean
  is_featured: boolean
  is_customizable: boolean
  max_ingredients: number
  display_order: number
  created_at: string
  updated_at: string
  category_name?: string // Added to store the parent category name
}

interface SubcategoryIngredient {
  id: string
  subcategory_id: string
  ingredient_id: string
  quantity: number
  is_default: boolean
  is_optional: boolean
  additional_price: number
  created_at: string
}

export default function MenuPage() {
  const { isDarkTheme, mounted } = useTheme()
  const [activeTab, setActiveTab] = useState('categories')
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  
  // Development mode setup
  const isDevelopment = process.env.NODE_ENV === 'development'
  const adminClient = isDevelopment ? createServerSupabaseClient() : supabase
  
  // Data states
  const [categories, setCategories] = useState<MenuCategory[]>([])
  const [ingredientCategories, setIngredientCategories] = useState<IngredientCategory[]>([])
  const [ingredients, setIngredients] = useState<Ingredient[]>([])
  const [subcategories, setSubcategories] = useState<Subcategory[]>([])
  const [subcategoryIngredients, setSubcategoryIngredients] = useState<SubcategoryIngredient[]>([])

  // Modal states
  const [showCategoryModal, setShowCategoryModal] = useState(false)
  const [showIngredientModal, setShowIngredientModal] = useState(false)
  const [showSubcategoryModal, setShowSubcategoryModal] = useState(false)
  const [editingItem, setEditingItem] = useState<any>(null)

  // Filter states
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(false)

  useEffect(() => {
    // Show development authentication message
    if (isDevelopment) {
      console.log('No session found in development, creating mock authentication')
      toast.success('Development mode: Using admin privileges', { duration: 2000 })
    }
    loadData()
  }, [activeTab])

  const loadData = async () => {
    setLoading(true)
    try {
      // Test database connection first using admin client
      const { data: connectionTest, error: connectionError } = await adminClient
        .from('menu_categories')
        .select('*')
        .limit(1)

      if (connectionError) {
        console.error('Database connection error:', connectionError)
        toast.error(`Database connection failed: ${connectionError.message}`)
        return
      }

      if (activeTab === 'categories') {
        await loadCategories()
      } else if (activeTab === 'ingredients') {
        await loadIngredients()
      } else if (activeTab === 'subcategories') {
        await loadSubcategories()
      }
    } catch (error) {
      console.error('Error loading data:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Failed to load data: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const { data, error } = await adminClient
        .from('categories')
        .select('*')
        .order('sort_order', { ascending: true })

      if (error) {
        console.error('Error loading categories:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        })
        toast.error(`Failed to load categories: ${error.message}`)
        setCategories([])
        return
      }
      
      // Map database fields to expected form fields
      const categories = (data || []).map((category: any) => ({
        ...category,
        name: category.name_en || category.name_el || '', // Use English first, fallback to Greek
        display_order: category.sort_order || 0, // Map sort_order to display_order for form compatibility
        // Add missing fields with defaults for compatibility
        category_type: 'standard', // Default since this field doesn't exist in DB
        is_featured: false // Default since this field doesn't exist in DB
      }))
      
      setCategories(categories)
      if (categories.length === 0) {
        console.log('Categories table is empty - no data found')
      }
    } catch (error) {
      console.error('Categories load error:', error)
      setCategories([])
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Unable to load categories: ${errorMessage}`)
    }
  }

  const loadIngredients = async () => {
    try {
      const [ingredientCategoriesResult, ingredientsResult] = await Promise.all([
        adminClient
          .from('ingredient_categories')
          .select('*')
          .order('display_order', { ascending: true }),
        adminClient
          .from('ingredients')
          .select('*')
      ])

      if (ingredientCategoriesResult.error) {
        console.error('Error loading ingredient categories:', {
          message: ingredientCategoriesResult.error.message,
          code: ingredientCategoriesResult.error.code,
          details: ingredientCategoriesResult.error.details,
          hint: ingredientCategoriesResult.error.hint
        })
        toast.error(`Failed to load ingredient categories: ${ingredientCategoriesResult.error.message}`)
        setIngredientCategories([])
      } else {
        const ingredientCategories = ingredientCategoriesResult.data || []
        setIngredientCategories(ingredientCategories)
        if (ingredientCategories.length === 0) {
          console.log('Ingredient categories table is empty - no data found')
        }
      }

      if (ingredientsResult.error) {
        console.error('Error loading ingredients:', {
          message: ingredientsResult.error.message,
          code: ingredientsResult.error.code,
          details: ingredientsResult.error.details,
          hint: ingredientsResult.error.hint
        })
        toast.error(`Failed to load ingredients: ${ingredientsResult.error.message}`)
        setIngredients([])
      } else {
        const ingredients = (ingredientsResult.data || []).map((ingredient: any) => ({
          ...ingredient,
          // Map multilingual fields to generic fields for UI compatibility
          name: ingredient.name_en || ingredient.name_el || ''
        }))
        setIngredients(ingredients)
        if (ingredients.length === 0) {
          console.log('Ingredients table is empty - no data found')
        }
      }
    } catch (error) {
      console.error('Ingredients load error:', error)
      setIngredientCategories([])
      setIngredients([])
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Unable to load ingredients: ${errorMessage}`)
    }
  }

  const loadSubcategories = async () => {
    try {
      const [subcategoriesResult, subcategoryIngredientsResult, categoriesResult] = await Promise.all([
        adminClient
          .from('subcategories')
          .select('*')
          .order('display_order', { ascending: true }),
        adminClient
          .from('subcategory_ingredients')
          .select('*'),
        adminClient
          .from('categories')
          .select('id, name_en')
      ])

      if (subcategoriesResult.error) {
        const error = subcategoriesResult.error
        console.error('Error loading subcategories:', {
          message: error.message || 'Unknown error',
          code: error.code || 'NO_CODE',
          details: error.details || 'No details available',
          hint: error.hint || 'No hint available'
        })
        console.error('Full subcategories error object:', JSON.stringify(error, null, 2))
        toast.error(`Failed to load subcategories: ${error.message || 'Unknown error'}`)
        setSubcategories([])
      } else {
        // Create a map of category IDs to names
        const categoryMap = new Map()
        if (categoriesResult.data) {
          categoriesResult.data.forEach((cat: any) => {
            categoryMap.set(cat.id, cat.name_en)
          })
        }

        const subcategories = (subcategoriesResult.data || []).map((item: any) => ({
          ...item,
          // Use the actual name field from the database
          name: item.name || '',
          description: item.description || '',
          base_price: item.price || 0,
          pickup_price: item.pickup_price || 0,
          delivery_price: item.delivery_price || 0,
          is_available: item.is_available ?? true,
          category_name: categoryMap.get(item.category_id) || 'Unknown Category'
        }))
        setSubcategories(subcategories)
        if (subcategories.length === 0) {
          console.log('Subcategories table is empty - no data found')
        }
      }

      if (subcategoryIngredientsResult.error) {
        const error = subcategoryIngredientsResult.error
        console.error('Error loading subcategory ingredients:', {
          message: error.message || 'Unknown error',
          code: error.code || 'NO_CODE',
          details: error.details || 'No details available',
          hint: error.hint || 'No hint available'
        })
        console.error('Full subcategory ingredients error object:', JSON.stringify(error, null, 2))
        toast.error(`Failed to load subcategory ingredients: ${error.message || 'Unknown error'}`)
        setSubcategoryIngredients([])
      } else {
        const ingredients = subcategoryIngredientsResult.data || []
        setSubcategoryIngredients(ingredients)
        if (ingredients.length === 0) {
          console.log('Subcategory ingredients table is empty - no data found')
        }
      }
    } catch (error) {
      console.error('Subcategories load error:', error)
      setSubcategories([])
      setSubcategoryIngredients([])
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Unable to load subcategories: ${errorMessage}`)
    }
  }

  const deleteCategory = async (id: string) => {
    if (!confirm('Are you sure you want to delete this category?')) return

    try {
      const { error } = await adminClient
        .from('categories')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('Category deleted successfully')
      loadCategories()
    } catch (error) {
      console.error('Error deleting category:', error)
      toast.error('Failed to delete category')
    }
  }

  const deleteIngredient = async (id: string) => {
    if (!confirm('Are you sure you want to delete this ingredient?')) return

    try {
      const { error } = await adminClient
        .from('ingredients')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('Ingredient deleted successfully')
      loadIngredients()
    } catch (error) {
      console.error('Error deleting ingredient:', error)
      toast.error('Failed to delete ingredient')
    }
  }

  const deleteSubcategory = async (id: string) => {
    if (!confirm('Are you sure you want to delete this subcategory?')) return

    try {
      const { error } = await adminClient
        .from('subcategories')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('Subcategory deleted successfully')
      loadSubcategories()
    } catch (error) {
      console.error('Error deleting subcategory:', error)
      toast.error('Failed to delete subcategory')
    }
  }

  const toggleAvailability = async (table: string, id: string, currentStatus: boolean) => {
    try {
      // Different tables use different field names for availability
      const fieldName = table === 'menu_categories' ? 'is_active' : 'is_available'
      
      const { error } = await adminClient
        .from(table)
        .update({ 
          [fieldName]: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)

      if (error) throw error
      toast.success('Status updated successfully')
      loadData()
    } catch (error) {
      console.error('Error updating status:', error)
      toast.error('Failed to update status')
    }
  }

  const handleCategorySave = async (categoryData: any) => {
    try {
      // Map form data to database schema (multilingual)
      const dbData = {
        name_en: categoryData.name || '',
        name_el: categoryData.name || '', // Use same value for both languages for now
        sort_order: categoryData.display_order || 0,
        is_active: categoryData.is_active ?? true,
        updated_at: new Date().toISOString()
      }

      if (categoryData.id) {
        // Update existing category
        const { error } = await adminClient
          .from('categories')
          .update(dbData)
          .eq('id', categoryData.id)

        if (error) throw error
        toast.success('Category updated successfully')
      } else {
        // Create new category - remove updated_at for insert
        const { updated_at, ...insertData } = dbData
        const { error } = await adminClient
          .from('categories')
          .insert(insertData)

        if (error) throw error
        toast.success('Category created successfully')
      }

      loadCategories()
      setEditingItem(null)
    } catch (error) {
      console.error('Error saving category:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        error: error,
        categoryData: categoryData
      })
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Failed to save category: ${errorMessage}`)
    }
  }

  const handleIngredientSave = async (ingredientData: any) => {
    try {
      // Map form data to database schema
      const dbData = {
        name_en: ingredientData.name || '',
        name_el: ingredientData.name || '', // Use same value for both languages for now
        name: ingredientData.name || '', // Also include single name field
        stock_quantity: ingredientData.stock_quantity || 0,
        unit: ingredientData.unit || 'unit',
        allergen_info: ingredientData.allergens || [], // Note: field name is allergen_info, not allergens
        is_active: ingredientData.is_active ?? true,
        minimum_stock: ingredientData.min_stock_level || ingredientData.minimum_stock_level || 0, // Note: field name is minimum_stock
        cost_per_unit: ingredientData.cost_per_unit || null,
        updated_at: new Date().toISOString()
      }

      if (ingredientData.id) {
        // Update existing ingredient
        const { error } = await adminClient
          .from('ingredients')
          .update(dbData)
          .eq('id', ingredientData.id)
        
        if (error) throw error
        toast.success('Ingredient updated successfully')
      } else {
        // Create new ingredient - remove updated_at for insert
        const { updated_at, ...insertData } = dbData
        const { error } = await adminClient
          .from('ingredients')
          .insert(insertData)
        
        if (error) throw error
        toast.success('Ingredient created successfully')
      }
      
      loadIngredients()
      setEditingItem(null)
    } catch (error) {
      console.error('Error saving ingredient:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        error: error,
        ingredientData: ingredientData
      })
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Failed to save ingredient: ${errorMessage}`)
    }
  }

  const handleSubcategoriesSave = async (subcategoryData: any) => {
    try {
      // Map form data to database schema (single language fields)
      const dbData = {
        category_id: subcategoryData.category_id,
        name: subcategoryData.name || '',
        description: subcategoryData.description || null,
        price: subcategoryData.pickup_price || 0, // Keep legacy price field for backward compatibility
        pickup_price: subcategoryData.pickup_price || 0,
        delivery_price: subcategoryData.delivery_price || 0,
        image_url: subcategoryData.image_url || null,
        is_available: subcategoryData.is_available ?? true,
        allergens: subcategoryData.allergens || [],
        preparation_time: subcategoryData.preparation_time || null,
        display_order: subcategoryData.display_order || 0,
        updated_at: new Date().toISOString()
      }

      if (subcategoryData.id) {
        // Update existing subcategory
        const { error } = await adminClient
          .from('subcategories')
          .update(dbData)
          .eq('id', subcategoryData.id)
        
        if (error) throw error
        toast.success('Subcategory updated successfully')
      } else {
        // Create new subcategory - remove updated_at for insert
        const { updated_at, ...insertData } = dbData
        const { error } = await adminClient
          .from('subcategories')
          .insert(insertData)
        
        if (error) throw error
        toast.success('Subcategory created successfully')
      }
      
      loadSubcategories()
      setEditingItem(null)
    } catch (error) {
      console.error('Error saving subcategory:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        error: error,
        subcategoryData: subcategoryData
      })
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Failed to save subcategory: ${errorMessage}`)
    }
  }

  const getStockStatus = (ingredient: Ingredient) => {
    if (ingredient.stock_quantity <= 0) return { status: 'out', color: 'bg-red-500/20 text-red-300', label: 'Out of Stock' }
    if (ingredient.stock_quantity <= ingredient.min_stock_level) return { status: 'low', color: 'bg-yellow-500/20 text-yellow-300', label: 'Low Stock' }
    return { status: 'good', color: 'bg-green-500/20 text-green-300', label: 'In Stock' }
  }

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredIngredients = ingredients.filter(ingredient => {
    const matchesSearch = ingredient.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAvailability = !showOnlyAvailable || ingredient.is_available
    return matchesSearch && matchesAvailability
  })

  const filteredSubcategories = subcategories.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || item.category_id === selectedCategory
    const matchesAvailability = !showOnlyAvailable || item.is_available
    return matchesSearch && matchesCategory && matchesAvailability
  })

  const renderCategoriesTab = () => (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6" data-cy="categories-stats">
        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Tag className="h-6 w-6 text-blue-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-blue-500/20 text-blue-300">Total</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>{categories.length}</div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Categories</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Star className="h-6 w-6 text-yellow-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-yellow-500/20 text-yellow-300">Featured</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {categories.filter(c => c.is_featured).length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Featured</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <ChefHat className="h-6 w-6 text-purple-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-purple-500/20 text-purple-300">Custom</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {categories.filter(c => c.category_type === 'customizable').length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Customizable</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Eye className="h-6 w-6 text-green-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-green-500/20 text-green-300">Active</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {categories.filter(c => c.is_active).length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Active</div>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-cy="categories-grid">
        {filteredCategories.length === 0 ? (
          <div className="col-span-full text-center py-12" data-cy="empty-state">
            <div className={`text-6xl mb-4 ${isDarkTheme ? 'text-white/50' : 'text-black/50'}`}>🏪</div>
            <h3 className={`text-xl font-semibold mb-2 ${isDarkTheme ? 'text-white' : 'text-black'}`}>
              No categories found
            </h3>
            <p className={`text-sm ${isDarkTheme ? 'text-white/70' : 'text-black/70'}`}>
              Categories will appear here once your database is properly configured.
            </p>
          </div>
        ) : (
          filteredCategories.map((category) => (
          <div
            key={category.id}
            data-cy="category-card"
            className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-300 ${
              isDarkTheme
                ? 'bg-white/10 border-white/20 hover:bg-white/15'
                : 'bg-black/10 border-black/20 hover:bg-black/15'
            }`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className="text-2xl mr-3">
                  {category.category_type === 'customizable' ? '🔧' : '📂'}
                </div>
                <div>
                  <h3 className={`text-lg font-bold transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`} data-cy="category-name">{category.name}</h3>
                  {category.parent_id && (
                    <p className={`text-sm transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/50' : 'text-black/50'
                    }`}>
                      Subcategory of {categories.find(c => c.id === category.parent_id)?.name}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {category.is_featured && <Star className="h-4 w-4 text-yellow-400" />}
                <span className={`px-2 py-1 rounded-full text-xs ${
                  category.is_active ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
                }`}>
                  {category.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>

            {category.description && (
              <p className={`text-sm mb-4 transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/70' : 'text-black/70'
              }`}>{category.description}</p>
            )}

            <div className="flex items-center justify-between mb-4">
              <span className={`px-3 py-1 rounded-full text-xs ${
                category.category_type === 'customizable'
                  ? 'bg-purple-500/20 text-purple-300'
                  : 'bg-blue-500/20 text-blue-300'
              }`}>
                {category.category_type === 'customizable' ? 'Customizable' : 'Standard'}
              </span>
              <span className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/50' : 'text-black/50'
              }`}>Order: {category.display_order}</span>
            </div>
            
            <div className="flex space-x-2" data-cy="category-actions">
              <button 
                onClick={() => {
                  setEditingItem(category)
                  setShowCategoryModal(true)
                }}
                data-cy="edit-button"
                aria-label="Edit category"
                className="flex-1 bg-blue-500/20 text-blue-300 py-2 rounded-lg text-sm hover:bg-blue-500/30 transition-colors flex items-center justify-center"
              >
                <Edit3 className="h-4 w-4 mr-1" />
                Edit
              </button>
              <button 
                onClick={() => toggleAvailability('menu_categories', category.id, category.is_active)}
                className={`flex-1 py-2 rounded-lg text-sm transition-colors flex items-center justify-center ${
                  category.is_active 
                    ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30' 
                    : 'bg-green-500/20 text-green-300 hover:bg-green-500/30'
                }`}
              >
                {category.is_active ? 'Disable' : 'Enable'}
              </button>
              <button 
                onClick={() => deleteCategory(category.id)}
                data-cy="delete-button"
                aria-label="Delete category"
                className="flex-1 bg-red-500/20 text-red-300 py-2 rounded-lg text-sm hover:bg-red-500/30 transition-colors flex items-center justify-center"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </button>
            </div>
          </div>
        )))}
      </div>
    </div>
  )

  const renderIngredientsTab = () => (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Package className="h-6 w-6 text-blue-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-blue-500/20 text-blue-300">Total</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>{ingredients.length}</div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Ingredients</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Eye className="h-6 w-6 text-green-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-green-500/20 text-green-300">Available</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {ingredients.filter(i => i.is_available).length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Available</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <AlertTriangle className="h-6 w-6 text-yellow-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-yellow-500/20 text-yellow-300">Low Stock</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {ingredients.filter(i => i.stock_quantity <= i.min_stock_level && i.stock_quantity > 0).length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Low Stock</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <X className="h-6 w-6 text-red-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-red-500/20 text-red-300">Out</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {ingredients.filter(i => i.stock_quantity <= 0).length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Out of Stock</div>
        </div>
      </div>

      {/* Ingredient Categories */}
      <div className="space-y-4">
        {ingredientCategories.map((category) => {
          const categoryIngredients = ingredients.filter(i => i.category_id === category.id)

          return (
            <div key={category.id} className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
              isDarkTheme
                ? 'bg-white/10 border-white/20'
                : 'bg-black/10 border-black/20'
            }`}>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div 
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: category.color_code }}
                  ></div>
                  <h3 className={`text-xl font-bold transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>{category.name}</h3>
                  <span className={`ml-3 px-2 py-1 rounded-full text-sm transition-all duration-1000 ${
                    isDarkTheme ? 'bg-white/10 text-white/70' : 'bg-black/10 text-black/70'
                  }`}>
                    {categoryIngredients.length} items
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categoryIngredients.map((ingredient) => {
                  const stockStatus = getStockStatus(ingredient)
                  
                  return (
                    <div
                      key={ingredient.id}
                      className={`backdrop-blur-md border rounded-xl p-4 transition-all duration-300 ${
                        isDarkTheme
                          ? 'bg-white/5 border-white/10 hover:bg-white/10'
                          : 'bg-black/5 border-black/10 hover:bg-black/10'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <h4 className={`font-medium transition-colors duration-1000 ${
                          isDarkTheme ? 'text-white' : 'text-black'
                        }`}>{ingredient.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs ${stockStatus.color}`}>
                          {stockStatus.label}
                        </span>
                      </div>

                      {ingredient.description && (
                        <p className={`text-sm mb-3 transition-colors duration-1000 ${
                          isDarkTheme ? 'text-white/60' : 'text-black/60'
                        }`}>{ingredient.description}</p>
                      )}
                      
                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-sm">
                          <span className={`transition-colors duration-1000 ${
                            isDarkTheme ? 'text-white/70' : 'text-black/70'
                          }`}>Price:</span>
                          <span className={`transition-colors duration-1000 ${
                            isDarkTheme ? 'text-white' : 'text-black'
                          }`}>${ingredient.price.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className={`transition-colors duration-1000 ${
                            isDarkTheme ? 'text-white/70' : 'text-black/70'
                          }`}>Stock:</span>
                          <span className={`transition-colors duration-1000 ${
                            isDarkTheme ? 'text-white' : 'text-black'
                          }`}>{ingredient.stock_quantity}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className={`transition-colors duration-1000 ${
                            isDarkTheme ? 'text-white/70' : 'text-black/70'
                          }`}>Min Level:</span>
                          <span className={`transition-colors duration-1000 ${
                            isDarkTheme ? 'text-white' : 'text-black'
                          }`}>{ingredient.min_stock_level}</span>
                        </div>
                      </div>

                      {ingredient.allergens && ingredient.allergens.length > 0 && (
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {ingredient.allergens.map((allergen, index) => (
                              <span key={index} className="px-2 py-1 bg-orange-500/20 text-orange-300 rounded text-xs">
                                {allergen}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <div className="flex space-x-2">
                        <button 
                          onClick={() => {
                            setEditingItem(ingredient)
                            setShowIngredientModal(true)
                          }}
                          className="flex-1 bg-blue-500/20 text-blue-300 py-2 rounded-lg text-sm hover:bg-blue-500/30 transition-colors flex items-center justify-center"
                        >
                          <Edit3 className="h-3 w-3 mr-1" />
                          Edit
                        </button>
                        <button 
                          onClick={() => toggleAvailability('ingredients', ingredient.id, ingredient.is_available)}
                          className={`flex-1 py-2 rounded-lg text-sm transition-colors flex items-center justify-center ${
                            ingredient.is_available 
                              ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30' 
                              : 'bg-green-500/20 text-green-300 hover:bg-green-500/30'
                          }`}
                        >
                          {ingredient.is_available ? 'Disable' : 'Enable'}
                        </button>
                        <button 
                          onClick={() => deleteIngredient(ingredient.id)}
                          className="flex-1 bg-red-500/20 text-red-300 py-2 rounded-lg text-sm hover:bg-red-500/30 transition-colors flex items-center justify-center"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )

  const renderSubcategoriesTab = () => (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Coffee className="h-6 w-6 text-blue-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-blue-500/20 text-blue-300">Total</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>{subcategories.length}</div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Subcategories</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Eye className="h-6 w-6 text-green-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-green-500/20 text-green-300">Active</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {subcategories.filter(i => i.is_available).length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Active</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Calendar className="h-6 w-6 text-yellow-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-yellow-500/20 text-yellow-300">Recent</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {subcategories.filter(i => {
              const createdDate = new Date(i.created_at);
              const weekAgo = new Date();
              weekAgo.setDate(weekAgo.getDate() - 7);
              return createdDate > weekAgo;
            }).length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>This Week</div>
        </div>

        <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <Package className="h-6 w-6 text-purple-400" />
            <span className="text-sm px-2 py-1 rounded-full bg-purple-500/20 text-purple-300">Ingredients</span>
          </div>
          <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>
            {subcategoryIngredients.length}
          </div>
          <div className={`text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Total Links</div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-3">
        <button
          onClick={() => setSelectedCategory('all')}
          className={`px-4 py-2 rounded-full backdrop-blur-md border transition-all duration-300 ${
            selectedCategory === 'all'
              ? isDarkTheme
                ? 'bg-white/20 border-white/30 text-white'
                : 'bg-black/20 border-black/30 text-black'
              : isDarkTheme
                ? 'bg-white/10 border-white/20 text-white/70 hover:bg-white/15'
                : 'bg-black/10 border-black/20 text-black/70 hover:bg-black/15'
          }`}
        >
          All Items ({subcategories.length})
        </button>
        {categories.map((category) => {
          const count = subcategories.filter(item => item.category_id === category.id).length
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-full backdrop-blur-md border transition-all duration-300 ${
                selectedCategory === category.id
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/30 text-white'
                    : 'bg-black/20 border-black/30 text-black'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 text-white/70 hover:bg-white/15'
                    : 'bg-black/10 border-black/20 text-black/70 hover:bg-black/15'
              }`}
            >
              {category.name} ({count})
            </button>
          )
        })}
      </div>

      {/* Subcategories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSubcategories.map((item) => {
          const itemIngredients = subcategoryIngredients.filter(mi => mi.subcategory_id === item.id)
          
          return (
            <div
              key={item.id}
              className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-300 ${
                isDarkTheme
                  ? 'bg-white/10 border-white/20 hover:bg-white/15'
                  : 'bg-black/10 border-black/20 hover:bg-black/15'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className={`text-lg font-bold transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>{item.name}</h3>
                  <p className={`text-sm transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/50' : 'text-black/50'
                  }`}>
                    {item.category_name || 'Unknown Category'}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    item.is_available ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
                  }`}>
                    {item.is_available ? 'Available' : 'Unavailable'}
                  </span>
                </div>
              </div>
              
              {item.description && (
                <p className={`text-sm mb-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/70' : 'text-black/70'
                }`}>{item.description}</p>
              )}

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className={`transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/70' : 'text-black/70'
                  }`}>Display Order:</span>
                  <span className={`font-medium transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>{item.display_order || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className={`transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/70' : 'text-black/70'
                  }`}>Created:</span>
                  <span className={`transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>{new Date(item.created_at).toLocaleDateString()}</span>
                </div>
                {itemIngredients.length > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className={`transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/70' : 'text-black/70'
                    }`}>Ingredients:</span>
                    <span className={`transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white' : 'text-black'
                    }`}>{itemIngredients.length}</span>
                  </div>
                )}
              </div>
              
              <div className="flex space-x-2">
                <button 
                  onClick={() => {
                    setEditingItem(item)
                    setShowSubcategoryModal(true)
                  }}
                  className="flex-1 bg-blue-500/20 text-blue-300 py-2 rounded-lg text-sm hover:bg-blue-500/30 transition-colors flex items-center justify-center"
                >
                  <Edit3 className="h-3 w-3 mr-1" />
                  Edit
                </button>
                <button 
                  onClick={() => toggleAvailability('subcategories', item.id, item.is_available)}
                  className={`flex-1 py-2 rounded-lg text-sm transition-colors flex items-center justify-center ${
                    item.is_available 
                      ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30' 
                      : 'bg-green-500/20 text-green-300 hover:bg-green-500/30'
                  }`}
                >
                  {item.is_available ? 'Disable' : 'Enable'}
                </button>
                <button 
                  onClick={() => deleteSubcategory(item.id)}
                  className="flex-1 bg-red-500/20 text-red-300 py-2 rounded-lg text-sm hover:bg-red-500/30 transition-colors flex items-center justify-center"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Delete
                </button>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )

  const tabs = [
    { id: 'categories', name: 'Categories', icon: Tag, description: 'Manage main menu categories' },
    { id: 'subcategories', name: 'Subcategories', icon: Coffee, description: 'Manage subcategories within categories' },
    { id: 'ingredients', name: 'Ingredients', icon: Package, description: 'Manage ingredients and stock levels' }
  ]

  // Use a consistent theme for initial render to prevent hydration mismatch
  // Always use dark theme classes until mounted to prevent hydration mismatch

  return (
      <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8" suppressHydrationWarning>
        {/* Header */}
        <div>
          <h1 className={`text-3xl font-bold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Enhanced Menu Management</h1>
          <p className={`mt-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>
            Manage categories, ingredients, and subcategories with advanced customization
          </p>
        </div>
        
        <div className="max-w-7xl mx-auto" suppressHydrationWarning>
          <div className="flex items-center justify-end mb-8">
            <div>
              <div className="flex space-x-3">
                <button
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
                </button>
                <button
                  onClick={() => loadData()}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </button>
                <button
                  onClick={() => {
                    setEditingItem(null)
                    if (activeTab === 'categories') setShowCategoryModal(true)
                    else if (activeTab === 'ingredients') setShowIngredientModal(true)
                    else if (activeTab === 'subcategories') setShowSubcategoryModal(true)
                  }}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New
                </button>
              </div>
            </div>
          </div>

        {/* Search and Filters */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-1000 ${
              isDarkTheme ? 'text-white/50' : 'text-black/50'
            }`} />
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-3 backdrop-blur-md border rounded-xl focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all duration-1000 ${
                isDarkTheme
                  ? 'bg-white/10 border-white/20 text-white placeholder-white/40'
                  : 'bg-black/10 border-black/20 text-black placeholder-black/40'
              }`}
            />
          </div>
          <div className="flex space-x-3">
            <label className={`flex items-center px-4 py-3 backdrop-blur-md border rounded-xl cursor-pointer transition-all duration-1000 ${
              isDarkTheme
                ? 'bg-white/10 border-white/20 text-white'
                : 'bg-black/10 border-black/20 text-black'
            }`} data-cy="availability-filter-label">
              <input
                type="checkbox"
                checked={showOnlyAvailable}
                onChange={(e) => setShowOnlyAvailable(e.target.checked)}
                className="mr-2"
              />
              Available Only
            </label>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:w-64">
            <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
              isDarkTheme
                ? 'bg-white/10 border-white/20'
                : 'bg-black/10 border-black/20'
            }`}>
              <div className="space-y-3">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-4 py-3 rounded-xl text-left transition-all duration-300 ${
                        activeTab === tab.id
                          ? isDarkTheme
                            ? 'bg-white/20 border border-white/30 text-white'
                            : 'bg-black/20 border border-black/30 text-black'
                          : isDarkTheme
                            ? 'text-white/70 hover:bg-white/10 hover:text-white'
                            : 'text-black/70 hover:bg-black/10 hover:text-black'
                      }`}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      <div>
                        <div className="font-medium">{tab.name}</div>
                        <div className={`text-xs transition-colors duration-1000 ${
                          isDarkTheme ? 'text-white/50' : 'text-black/50'
                        }`}>{tab.description}</div>
                      </div>
                    </button>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <RefreshCw className={`h-8 w-8 animate-spin mx-auto mb-4 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`} />
                  <p className={`transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/70' : 'text-black/70'
                  }`}>Loading...</p>
                </div>
              </div>
            ) : (
              <>
                {activeTab === 'categories' && renderCategoriesTab()}
                {activeTab === 'ingredients' && renderIngredientsTab()}
                {activeTab === 'subcategories' && renderSubcategoriesTab()}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      <CategoryModal
        isOpen={showCategoryModal}
        onClose={() => {
          setShowCategoryModal(false)
          setEditingItem(null)
        }}
        onSave={handleCategorySave}
        category={editingItem}
      />

      <IngredientModal
        isOpen={showIngredientModal}
        onClose={() => {
          setShowIngredientModal(false)
          setEditingItem(null)
        }}
        onSave={handleIngredientSave}
        ingredient={editingItem}
        categories={ingredientCategories}
      />

      <SubcategoryModal
        isOpen={showSubcategoryModal}
        onClose={() => {
          setShowSubcategoryModal(false)
          setEditingItem(null)
        }}
        onSave={handleSubcategoriesSave}
        subcategory={editingItem}
        categories={categories}
        ingredients={ingredients}
      />
      </div>
  )
}
