import React, { memo } from 'react';
import { useTheme } from '../contexts/theme-context';

interface FloatingActionButtonProps {
  onClick: () => void;
  className?: string;
}

const FloatingActionButton = memo<FloatingActionButtonProps>(({ 
  onClick,
  className = '' 
}) => {
  const { resolvedTheme } = useTheme();

  return (
    <button 
      className={`
        fixed bottom-6 right-6 z-50
        w-16 h-16 rounded-full
        flex items-center justify-center
        transition-all duration-300 ease-out
        transform hover:scale-110 active:scale-95
        backdrop-blur-lg border-2
        shadow-2xl hover:shadow-3xl
        cursor-pointer hover:shadow-[0_0_30px_rgba(59,130,246,0.5)]
        bg-gradient-to-br from-blue-500 to-blue-600 border-blue-400/50 text-white
        ${className}
      `}
      style={{
        background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(37, 99, 235, 0.95) 100%)',
        backdropFilter: 'blur(20px)',
        boxShadow: 'rgba(59, 130, 246, 0.3) 0px 8px 32px, rgba(0, 0, 0, 0.1) 0px 4px 16px'
      }}
      onClick={onClick}
    >
      <span className="text-2xl font-bold drop-shadow-lg">+</span>
    </button>
  );
});

FloatingActionButton.displayName = 'FloatingActionButton';
export default FloatingActionButton; 