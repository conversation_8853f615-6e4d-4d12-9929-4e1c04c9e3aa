// OrderService for POS system - API integration with admin dashboard
import { Order, OrderStatus } from '../renderer/types/orders';
import { environment, getApiUrl, isDevelopment } from '../config/environment';

// Utility functions
const shouldDebugLog = (): boolean => {
  return environment.DEBUG_LOGGING && isDevelopment();
};

export class OrderService {
  private static instance: OrderService;
  
  public static getInstance(): OrderService {
    if (!OrderService.instance) {
      OrderService.instance = new OrderService();
    }
    return OrderService.instance;
  }

  // Fetch orders from admin dashboard API
  async fetchOrders(): Promise<Order[]> {
    try {
      const response = await fetch(getApiUrl('/orders'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const orders = await response.json();
      
      return orders;
    } catch (error) {
      // Log error for debugging in development
      if (shouldDebugLog()) {
        console.error('❌ Failed to fetch orders from API:', error);
      }
      throw error;
    }
  }

  // Update order status
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<void> {
    try {
      const response = await fetch(getApiUrl(`/orders/${orderId}/status`), {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Failed to update order status:', error);
      throw error;
    }
  }

  // Create new order
  async createOrder(orderData: Partial<Order>): Promise<Order> {
    try {
      const response = await fetch(getApiUrl('/orders'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const newOrder = await response.json();
      
      return newOrder;
    } catch (error) {
      // Log error for debugging in development
      if (shouldDebugLog()) {
        console.error('❌ Failed to create order:', error);
      }
      throw error;
    }
  }

  // Delete order
  async deleteOrder(orderId: string): Promise<void> {
    try {
      const response = await fetch(getApiUrl(`/orders/${orderId}`), {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Failed to delete order:', error);
      throw error;
    }
  }
}