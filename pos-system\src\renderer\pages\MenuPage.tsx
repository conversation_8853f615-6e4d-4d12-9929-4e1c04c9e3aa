import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { useOrderStore } from '../hooks/useOrderStore';
import { useTheme } from '../contexts/theme-context';
import { supabase } from '../../shared/supabase';

// Import modular components
import { MenuGrid, MenuItemModal, CustomerInfoModal, CartSummary } from '../components/menu';
import { MenuItem, Ingredient, MenuCategory, MenuService } from '../services/MenuService';

interface SelectedIngredient {
  ingredient: Ingredient;
  quantity: number;
}

interface SelectedCustomization {
  customizationId: string;
  optionId: string;
  name: string;
  price: number;
}

interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  menuItemId?: string;
  basePrice?: number;
  customizations?: SelectedCustomization[];
  totalPrice?: number;
}

interface CustomerInfo {
  name: string;
  phone: string;
  address: string;
}

const MenuPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { createOrder } = useOrderStore();
  const { resolvedTheme } = useTheme();
  
  // State for menu and cart
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);
  const [cartItems, setCartItems] = useState<OrderItem[]>([]);
  const [orderType, setOrderType] = useState<"dine-in" | "takeaway" | "delivery">("takeaway");
  
  // Database state
  const [categories, setCategories] = useState<MenuCategory[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [mostFrequentedItems, setMostFrequentedItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Modal states
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    address: ''
  });

  // Initialize MenuService
  const menuService = MenuService.getInstance();

  useEffect(() => {
    // Set order type from URL parameter if provided
    const orderTypeParam = searchParams.get('orderType');
    if (orderTypeParam && ['dine-in', 'takeaway', 'delivery'].includes(orderTypeParam)) {
      setOrderType(orderTypeParam as 'dine-in' | 'takeaway' | 'delivery');
    }

    // Set customer info from URL parameters if provided
    const customerName = searchParams.get('customerName');
    const customerPhone = searchParams.get('customerPhone');
    const deliveryAddress = searchParams.get('deliveryAddress');
    const deliveryPostcode = searchParams.get('deliveryPostcode');
    const deliveryFloor = searchParams.get('deliveryFloor');
    const deliveryNotes = searchParams.get('deliveryNotes');

    if (customerName && customerPhone) {
      let fullAddress = deliveryAddress || '';
      if (deliveryPostcode) fullAddress += `, ${deliveryPostcode}`;
      if (deliveryFloor) fullAddress += `, Floor: ${deliveryFloor}`;
      if (deliveryNotes) fullAddress += `, Notes: ${deliveryNotes}`;

      setCustomerInfo({
        name: customerName,
        phone: customerPhone,
        address: fullAddress
      });
    }

    // Load menu data from database
    loadMenuData();
  }, [searchParams]);

  const loadMenuData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch categories and menu items from database
      const [categoriesData, menuItemsData] = await Promise.all([
        menuService.getMenuCategories(),
        menuService.getMenuItems()
      ]);

      setCategories(categoriesData);
      setMenuItems(menuItemsData);

      // Don't set initial selected category - start with no category selected to show most frequented

      // Fetch most frequented items (fallback to featured items)
      try {
        // Try to get most frequented items from the database function
        const { data: frequentedData, error: frequentedError } = await supabase
          .rpc('get_most_frequented_subcategories', { limit_count: 6 });
        
        if (frequentedError) {
          console.warn('Could not fetch most frequented items:', frequentedError);
          // Fallback to featured items
          const featuredItems = menuItemsData.filter(item => item.is_featured).slice(0, 6);
          setMostFrequentedItems(featuredItems);
        } else {
          setMostFrequentedItems(frequentedData || []);
        }
      } catch (err) {
        console.warn('Error fetching most frequented items:', err);
        // Fallback to featured items
        const featuredItems = menuItemsData.filter(item => item.is_featured).slice(0, 6);
        setMostFrequentedItems(featuredItems);
      }

    } catch (err) {
      console.error('Error loading menu data:', err);
      setError('Failed to load menu data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get filtered items based on selected category
  const filteredItems = selectedCategory 
    ? menuItems.filter(item => item.category_id === selectedCategory)
    : mostFrequentedItems;

  // Event handlers
  const handleAddToCart = (
    menuItem: MenuItem, 
    quantity: number, 
    customizations: SelectedIngredient[], 
    notes: string
  ) => {
    const customizationPrice = customizations.reduce((sum, c) => sum + (c.ingredient.price * c.quantity), 0);
    const itemTotalPrice = menuItem.price + customizationPrice;

    const orderItem: OrderItem = {
      id: `${menuItem.id}-${Date.now()}`,
      name: menuItem.name || menuItem.name_en || 'Unknown Item',
      quantity: quantity,
      price: itemTotalPrice,
      notes: notes || undefined,
      menuItemId: menuItem.id,
      basePrice: menuItem.price,
      customizations: customizations.map((c) => ({
        customizationId: c.ingredient.id,
        optionId: c.ingredient.id,
        name: c.ingredient.name,
        price: c.ingredient.price
      })),
      totalPrice: itemTotalPrice,
    };

    setCartItems(prev => [...prev, orderItem]);
    toast.success(`${menuItem.name} added to cart`);
  };

  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(itemId);
      return;
    }
    
    setCartItems(prev => 
      prev.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              quantity: newQuantity, 
              price: ((item.basePrice || 0) + (item.customizations || []).reduce((sum, c) => sum + c.price, 0)) * newQuantity 
            }
          : item
      )
    );
  };

  const handleRemoveItem = (itemId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== itemId));
    toast.success('Item removed from cart');
  };

  const handlePlaceOrder = async () => {
    if (cartItems.length === 0) {
      toast.error('Cart is empty');
      return;
    }

    if (!customerInfo.name || !customerInfo.phone) {
      toast.error('Please provide customer information');
      setShowCustomerModal(true);
      return;
    }

    if (orderType === 'delivery' && !customerInfo.address) {
      toast.error('Please provide delivery address');
      setShowCustomerModal(true);
      return;
    }

    try {
      const totalAmount = cartItems.reduce((sum, item) => sum + (item.totalPrice || item.price * item.quantity), 0);
      const tax = totalAmount * 0.1; // 10% tax
      const deliveryFee = orderType === 'delivery' ? 5.00 : 0;
      const finalTotal = totalAmount + tax + deliveryFee;

      const orderData = {
        items: cartItems.map(item => ({
          id: item.id,
          name: item.name,
          quantity: item.quantity,
          price: item.totalPrice || item.price,
          notes: item.notes
        })),
        total_amount: finalTotal,
        status: 'pending' as const,
        order_type: orderType,
        customer_name: customerInfo.name,
        customer_phone: customerInfo.phone,
        delivery_address: orderType === 'delivery' ? customerInfo.address : undefined,
        table_number: orderType === 'dine-in' ? customerInfo.address : undefined,
        special_instructions: orderType === 'takeaway' ? customerInfo.address : undefined,
      };

      await createOrder(orderData);
      toast.success('Order placed successfully!');
      navigate('/orders');
    } catch (error) {
      console.error('Failed to create order:', error);
      toast.error('Failed to place order. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Menu</h1>
            <button
              onClick={() => navigate('/orders')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              View Orders
            </button>
          </div>
          
          {/* Order Type Display */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-700">Order Type:</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium capitalize">
                {orderType.replace('-', ' ')}
              </span>
            </div>
            {cartItems.length > 0 && (
              <div className="text-sm text-gray-600">
                {cartItems.length} item{cartItems.length !== 1 ? 's' : ''} in cart
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Menu Items */}
          <div className="lg:col-span-2">
            {loading ? (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading menu...</p>
                  </div>
                </div>
              </div>
            ) : error ? (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <p className="text-red-600 font-medium">{error}</p>
                    <button 
                      onClick={loadMenuData}
                      className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <MenuGrid
                items={filteredItems.map(item => ({
                  id: item.id,
                  name: item.name || item.name_en || 'Unknown Item',
                  description: item.description || item.description_en || '',
                  price: item.base_price || item.price,
                  category: item.category || item.category_id,
                  preparationTime: item.preparation_time || item.preparationTime || 0,
                  image: item.image_url || undefined,
                  is_customizable: item.is_customizable || false
                }))}
                selectedCategory={selectedCategory}
                categories={categories.map(cat => ({
                  id: cat.id,
                  name: cat.name || cat.name_en || 'Unknown Category'
                }))}
                onCategoryChange={setSelectedCategory}
                onItemClick={(item) => {
                  // Find the original MenuItem to pass to the modal
                  const originalItem = filteredItems.find(orig => orig.id === item.id);
                  if (originalItem) {
                    // Check if item is customizable and trigger ingredients modal
                    if (originalItem.is_customizable) {
                      setSelectedMenuItem(originalItem);
                    } else {
                      // For non-customizable items, add directly to cart
                      handleAddToCart(originalItem, 1, [], '');
                    }
                  }
                }}
                showMostFrequented={!selectedCategory}
                mostFrequentedTitle="Most Popular Items"
                hideAllItemsButton={true}
              />
            )}
          </div>

          {/* Cart Summary */}
          <div className="lg:col-span-1">
            <CartSummary
              cartItems={cartItems}
              orderType={orderType}
              customerInfo={customerInfo}
              onEditCustomer={() => setShowCustomerModal(true)}
              onUpdateQuantity={handleUpdateQuantity}
              onRemoveItem={handleRemoveItem}
              onPlaceOrder={handlePlaceOrder}
            />
          </div>
        </div>
      </div>

      {/* Modals */}
      <MenuItemModal
        isOpen={!!selectedMenuItem}
        onClose={() => setSelectedMenuItem(null)}
        menuItem={selectedMenuItem}
        onAddToCart={handleAddToCart}
      />

      <CustomerInfoModal
        isOpen={showCustomerModal}
        onClose={() => setShowCustomerModal(false)}
        onSave={(info) => setCustomerInfo(info)}
        initialData={customerInfo}
        orderType={orderType}
      />
    </div>
  );
};

export default MenuPage;