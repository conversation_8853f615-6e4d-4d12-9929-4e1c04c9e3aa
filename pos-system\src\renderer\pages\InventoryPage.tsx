import React, { useState } from 'react';
import { useTheme } from '../contexts/theme-context';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  stock: number;
  price: number;
  lowStockThreshold: number;
  status: 'in-stock' | 'low-stock' | 'out-of-stock';
}

const InventoryPage: React.FC = () => {
  const { resolvedTheme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Mock inventory data
  const [inventoryItems] = useState<InventoryItem[]>([
    {
      id: '1',
      name: 'Margherita Pizza',
      category: 'Pizza',
      stock: 25,
      price: 12.99,
      lowStockThreshold: 10,
      status: 'in-stock'
    },
    {
      id: '2',
      name: 'Pepperoni Pizza',
      category: 'Pizza',
      stock: 8,
      price: 14.99,
      lowStockThreshold: 10,
      status: 'low-stock'
    },
    {
      id: '3',
      name: 'Caesar Salad',
      category: 'Salads',
      stock: 0,
      price: 8.99,
      lowStockThreshold: 5,
      status: 'out-of-stock'
    },
    {
      id: '4',
      name: 'Coca Cola',
      category: 'Beverages',
      stock: 50,
      price: 2.99,
      lowStockThreshold: 20,
      status: 'in-stock'
    }
  ]);

  const categories = ['all', ...Array.from(new Set(inventoryItems.map(item => item.category)))];

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in-stock': return 'text-green-600';
      case 'low-stock': return 'text-yellow-600';
      case 'out-of-stock': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'in-stock': return 'bg-green-100 text-green-800';
      case 'low-stock': return 'bg-yellow-100 text-yellow-800';
      case 'out-of-stock': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`min-h-screen p-6 ${resolvedTheme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Inventory Management</h1>
          <p className={`${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage your restaurant inventory and track stock levels
          </p>
        </div>

        {/* Controls */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search inventory items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full px-4 py-2 rounded-lg border ${
                resolvedTheme === 'dark' 
                  ? 'bg-gray-800 border-gray-700 text-white placeholder-gray-400' 
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              resolvedTheme === 'dark' 
                ? 'bg-gray-800 border-gray-700 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </option>
            ))}
          </select>
        </div>

        {/* Inventory Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredItems.map(item => (
            <div
              key={item.id}
              className={`p-6 rounded-lg border ${
                resolvedTheme === 'dark' 
                  ? 'bg-gray-800 border-gray-700' 
                  : 'bg-white border-gray-200'
              } shadow-sm hover:shadow-md transition-shadow`}
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="font-semibold text-lg">{item.name}</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(item.status)}`}>
                  {item.status.replace('-', ' ').toUpperCase()}
                </span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className={resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Category:</span>
                  <span>{item.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className={resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Price:</span>
                  <span className="font-medium">${item.price.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className={resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Stock:</span>
                  <span className={`font-medium ${getStatusColor(item.status)}`}>
                    {item.stock} units
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className={resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>Low Stock Alert:</span>
                  <span>{item.lowStockThreshold} units</span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                  Update Stock
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <p className={`text-lg ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
              No inventory items found matching your criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InventoryPage;